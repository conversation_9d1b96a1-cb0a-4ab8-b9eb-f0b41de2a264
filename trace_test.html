<!DOCTYPE html>
<html>
<head>
    <title>Trace Test - Template Loading</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 5px; margin: 2px 0; border-radius: 3px; font-size: 12px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warn { color: orange; }
    </style>
</head>
<body>
    <h1>🔍 Trace Test - Template Loading</h1>
    <div id="output" class="log"></div>
    
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const className = type;
            output.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function traceTest() {
            try {
                log('🔄 بدء اختبار التتبع المفصل...', 'info');
                
                const core = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore', 'success');
                
                // التحقق من القالب
                await core.preloadTemplate();
                log(`✅ القالب متاح: ${core.cachedTemplate ? core.cachedTemplate.length : 'null'} حرف`, 'success');
                
                // إعداد البيانات الوهمية
                const mockAnalysis = {
                    vulnerabilities: [],
                    total_vulnerabilities: 0,
                    pages_processed: 1,
                    scan_timestamp: new Date().toISOString()
                };
                
                // تتبع مفصل للتنفيذ
                log('🔄 بدء generateFinalComprehensiveReport...', 'info');
                
                // إعادة تعريف console.log مؤقتاً لتتبع التنفيذ
                const originalLog = console.log;
                const originalError = console.error;
                const originalWarn = console.warn;
                
                let logCount = 0;
                console.log = function(...args) {
                    logCount++;
                    if (logCount < 50) { // تحديد عدد الرسائل لتجنب الإفراط
                        log(`[LOG ${logCount}] ${args.join(' ')}`, 'info');
                    }
                    originalLog.apply(console, args);
                };
                
                console.error = function(...args) {
                    log(`[ERROR] ${args.join(' ')}`, 'error');
                    originalError.apply(console, args);
                };
                
                console.warn = function(...args) {
                    log(`[WARN] ${args.join(' ')}`, 'warn');
                    originalWarn.apply(console, args);
                };
                
                try {
                    // محاولة تشغيل الدالة مع تتبع مفصل
                    log('📞 استدعاء generateFinalComprehensiveReport...', 'info');
                    
                    const startTime = Date.now();
                    const report = await core.generateFinalComprehensiveReport(mockAnalysis, [], 'https://example.com');
                    const endTime = Date.now();
                    
                    log(`⏱️ وقت التنفيذ: ${endTime - startTime}ms`, 'info');
                    
                    if (report) {
                        log(`✅ نجح إنشاء التقرير: ${report.length} حرف`, 'success');
                        log(`📋 بداية التقرير: ${report.substring(0, 200)}...`, 'info');
                    } else {
                        log('❌ التقرير فارغ أو null', 'error');
                    }
                    
                } catch (error) {
                    log(`❌ خطأ في generateFinalComprehensiveReport:`, 'error');
                    log(`📋 رسالة الخطأ: ${error.message}`, 'error');
                    log(`📋 نوع الخطأ: ${error.name}`, 'error');
                    log(`📋 Stack trace: ${error.stack}`, 'error');
                    
                    // محاولة تحليل مكان الخطأ
                    if (error.stack) {
                        const stackLines = error.stack.split('\n');
                        stackLines.forEach((line, index) => {
                            if (index < 5) { // أول 5 أسطر من stack trace
                                log(`📋 Stack[${index}]: ${line.trim()}`, 'error');
                            }
                        });
                    }
                } finally {
                    // استعادة console الأصلي
                    console.log = originalLog;
                    console.error = originalError;
                    console.warn = originalWarn;
                }
                
                log('🏁 انتهى اختبار التتبع', 'info');
                
            } catch (error) {
                log(`❌ خطأ عام في الاختبار: ${error.message}`, 'error');
                log(`📋 Stack trace: ${error.stack}`, 'error');
            }
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(traceTest, 1000);
        });
    </script>
</body>
</html>
