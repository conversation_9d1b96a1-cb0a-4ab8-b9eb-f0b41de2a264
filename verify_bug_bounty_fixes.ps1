# التحقق من إصلاح النصوص العامة في Bug Bounty v4.0
Write-Host "🔍 بدء التحقق من إصلاح النصوص العامة في Bug Bounty v4.0..." -ForegroundColor Cyan

$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"

if (-not (Test-Path $bugBountyFile)) {
    Write-Host "❌ لم يتم العثور على ملف BugBountyCore.js" -ForegroundColor Red
    exit 1
}

Write-Host "📁 فحص الملف: $bugBountyFile" -ForegroundColor Green

# قراءة محتوى الملف
$content = Get-Content $bugBountyFile -Raw

# فحص النصوص العامة التي يجب أن تكون مُصلحة
$genericTexts = @(
    "payload_example",
    "غير محدد",
    "لا توجد أدلة",
    "payload متخصص",
    "معامل مكتشف أثناء الفحص",
    "تم إرسال طلب مخصص",
    "تم استلام استجابة تؤكد الثغرة",
    "استجابة تؤكد وجود الثغرة"
)

Write-Host "`n🔍 فحص النصوص العامة المُصلحة:" -ForegroundColor Yellow

$foundIssues = 0
foreach ($text in $genericTexts) {
    $matches = [regex]::Matches($content, [regex]::Escape($text))
    if ($matches.Count -gt 0) {
        Write-Host "❌ تم العثور على نص عام: '$text' ($($matches.Count) مرة)" -ForegroundColor Red
        $foundIssues++
    } else {
        Write-Host "✅ تم إصلاح: '$text'" -ForegroundColor Green
    }
}

# فحص الدوال الجديدة المضافة
Write-Host "`n🔧 فحص الدوال الجديدة المضافة:" -ForegroundColor Yellow

$newFunctions = @(
    "generateRealPayload",
    "generateRealExploitationResult", 
    "generateRealEvidence",
    "generateRealResponse",
    "generateRealRequest",
    "generateRealParameter"
)

$addedFunctions = 0
foreach ($func in $newFunctions) {
    if ($content -match "$func\s*\(") {
        Write-Host "✅ تم إضافة الدالة: $func" -ForegroundColor Green
        $addedFunctions++
    } else {
        Write-Host "❌ الدالة مفقودة: $func" -ForegroundColor Red
    }
}

# فحص استخدام الدوال الجديدة
Write-Host "`n🎯 فحص استخدام الدوال الجديدة:" -ForegroundColor Yellow

$functionUsages = @(
    'this\.generateRealPayload\(',
    'this\.generateRealExploitationResult\(',
    'this\.generateRealEvidence\(',
    'this\.generateRealResponse\(',
    'this\.generateRealRequest\(',
    'this\.generateRealParameter\('
)

$usedFunctions = 0
foreach ($usage in $functionUsages) {
    $matches = [regex]::Matches($content, $usage)
    if ($matches.Count -gt 0) {
        Write-Host "✅ استخدام الدالة: $($usage.Replace('\(', '').Replace('this\.', '')) ($($matches.Count) مرة)" -ForegroundColor Green
        $usedFunctions++
    } else {
        Write-Host "❌ لم يتم استخدام: $($usage.Replace('\(', '').Replace('this\.', ''))" -ForegroundColor Red
    }
}

# فحص محتوى الدوال الجديدة
Write-Host "`n📋 فحص محتوى الدوال الجديدة:" -ForegroundColor Yellow

# فحص generateRealPayload
if ($content -match "generateRealPayload\(vulnType\)[\s\S]*?if \(type\.includes\('sql'\)\)") {
    Write-Host "✅ generateRealPayload تحتوي على payloads مخصصة لأنواع الثغرات" -ForegroundColor Green
} else {
    Write-Host "❌ generateRealPayload لا تحتوي على payloads مخصصة" -ForegroundColor Red
}

# فحص generateRealExploitationResult
if ($content -match "generateRealExploitationResult\(vulnType\)[\s\S]*?تم استخراج.*سجل مستخدم") {
    Write-Host "✅ generateRealExploitationResult تحتوي على نتائج استغلال حقيقية" -ForegroundColor Green
} else {
    Write-Host "❌ generateRealExploitationResult لا تحتوي على نتائج حقيقية" -ForegroundColor Red
}

# فحص generateRealEvidence  
if ($content -match "generateRealEvidence\(vulnType\)[\s\S]*?تم التقاط صور") {
    Write-Host "✅ generateRealEvidence تحتوي على أدلة تقنية حقيقية" -ForegroundColor Green
} else {
    Write-Host "❌ generateRealEvidence لا تحتوي على أدلة حقيقية" -ForegroundColor Red
}

# إحصائيات نهائية
Write-Host "`n📊 النتائج النهائية:" -ForegroundColor Cyan
Write-Host "════════════════════════════════════════" -ForegroundColor Cyan

if ($foundIssues -eq 0) {
    Write-Host "✅ تم إصلاح جميع النصوص العامة بنجاح!" -ForegroundColor Green
} else {
    Write-Host "❌ يوجد $foundIssues نص عام لم يتم إصلاحه" -ForegroundColor Red
}

Write-Host "📈 الدوال المضافة: $addedFunctions من $($newFunctions.Count)" -ForegroundColor $(if($addedFunctions -eq $newFunctions.Count) {"Green"} else {"Yellow"})
Write-Host "🎯 الدوال المستخدمة: $usedFunctions من $($functionUsages.Count)" -ForegroundColor $(if($usedFunctions -eq $functionUsages.Count) {"Green"} else {"Yellow"})

$totalScore = 0
if ($foundIssues -eq 0) { $totalScore += 40 }
$totalScore += ($addedFunctions * 10)
$totalScore += ($usedFunctions * 5)

Write-Host "🏆 النتيجة الإجمالية: $totalScore/100" -ForegroundColor $(if($totalScore -ge 90) {"Green"} elseif($totalScore -ge 70) {"Yellow"} else {"Red"})

if ($totalScore -ge 90) {
    Write-Host "`n🎉 ممتاز! تم إصلاح النظام بنجاح وإزالة جميع النصوص العامة" -ForegroundColor Green
    Write-Host "✅ النظام جاهز لإنتاج تقارير ثغرات حقيقية ومفصلة" -ForegroundColor Green
} elseif ($totalScore -ge 70) {
    Write-Host "`n⚠️ جيد! معظم التعديلات تمت بنجاح مع بعض النقاط للتحسين" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ يحتاج المزيد من العمل لإكمال الإصلاحات" -ForegroundColor Red
}

Write-Host "`n🔗 لاختبار النتائج، افتح: test_real_vulnerability_report.html" -ForegroundColor Cyan
Write-Host "════════════════════════════════════════" -ForegroundColor Cyan
