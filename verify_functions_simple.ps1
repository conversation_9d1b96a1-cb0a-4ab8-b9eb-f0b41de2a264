# Bug Bounty v4.0 Dynamic Functions Verification
Write-Host "Checking dynamic functions updates..." -ForegroundColor Green

$bugBountyFile = "assets\modules\bugbounty\BugBountyCore.js"

if (-not (Test-Path $bugBountyFile)) {
    Write-Host "BugBountyCore.js file not found!" -ForegroundColor Red
    exit 1
}

Write-Host "Reading BugBountyCore.js file..." -ForegroundColor Yellow
$content = Get-Content $bugBountyFile -Raw

# Check updated function signatures
Write-Host "`nChecking updated function signatures..." -ForegroundColor Cyan

$functionsToCheck = @(
    "generateRealPayload\(vulnType, vulnerability = null\)",
    "generateRealEvidence\(vulnType, vulnerability = null\)",
    "generateRealResponse\(vulnType, vulnerability = null\)",
    "generateRealRequest\(vulnType, vulnerability = null\)",
    "generateRealParameter\(vulnerability = null\)",
    "generateRealExploitationResult\(vulnType, vulnerability = null\)"
)

$allFunctionsUpdated = $true

foreach ($func in $functionsToCheck) {
    if ($content -match $func) {
        Write-Host "Found: $func" -ForegroundColor Green
    } else {
        Write-Host "Missing: $func" -ForegroundColor Red
        $allFunctionsUpdated = $false
    }
}

# Check real data usage in functions
Write-Host "`nChecking real data usage..." -ForegroundColor Cyan

$realDataChecks = @(
    "vulnerability\.exploitation_result",
    "vulnerability\.testing_results",
    "vulnerability\.payload",
    "vulnerability\.evidence",
    "vulnerability\.response",
    "vulnerability\.request",
    "vulnerability\.parameter"
)

foreach ($check in $realDataChecks) {
    $matches = [regex]::Matches($content, $check)
    if ($matches.Count -gt 0) {
        Write-Host "Real data usage: $check ($($matches.Count) times)" -ForegroundColor Green
    } else {
        Write-Host "Not found: $check" -ForegroundColor Yellow
    }
}

# Check updated function calls
Write-Host "`nChecking updated function calls..." -ForegroundColor Cyan

$callsToCheck = @(
    "generateRealPayload\([^,]+, vuln\)",
    "generateRealEvidence\([^,]+, vuln\)",
    "generateRealResponse\([^,]+, vuln\)",
    "generateRealRequest\([^,]+, vuln\)",
    "generateRealParameter\(vuln\)",
    "generateRealExploitationResult\([^,]+, vuln"
)

$allCallsUpdated = $true

foreach ($call in $callsToCheck) {
    $matches = [regex]::Matches($content, $call)
    if ($matches.Count -gt 0) {
        Write-Host "Updated calls: $call ($($matches.Count) times)" -ForegroundColor Green
    } else {
        Write-Host "Missing updated calls: $call" -ForegroundColor Red
        $allCallsUpdated = $false
    }
}

# Final result
Write-Host "`nFinal Result:" -ForegroundColor Magenta

if ($allFunctionsUpdated -and $allCallsUpdated) {
    Write-Host "All dynamic functions updated successfully!" -ForegroundColor Green
    Write-Host "Functions now use real data from discovered vulnerabilities" -ForegroundColor Green
    Write-Host "All function calls updated to pass real data" -ForegroundColor Green
} else {
    Write-Host "Some issues need to be fixed:" -ForegroundColor Yellow
    
    if (-not $allFunctionsUpdated) {
        Write-Host "  - Some function signatures not updated" -ForegroundColor Red
    }
    
    if (-not $allCallsUpdated) {
        Write-Host "  - Some function calls not updated" -ForegroundColor Red
    }
}

# Additional statistics
Write-Host "`nAdditional Statistics:" -ForegroundColor Cyan

$totalLines = ($content -split "`n").Count
Write-Host "Total lines: $totalLines" -ForegroundColor White

$functionMatches = [regex]::Matches($content, "generateReal\w+\(")
Write-Host "Total dynamic functions: $($functionMatches.Count)" -ForegroundColor White

$vulnerabilityMatches = [regex]::Matches($content, "vulnerability\.")
Write-Host "Vulnerability object usages: $($vulnerabilityMatches.Count)" -ForegroundColor White

Write-Host "`nVerification completed!" -ForegroundColor Green
