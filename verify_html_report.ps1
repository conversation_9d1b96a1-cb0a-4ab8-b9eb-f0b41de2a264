# PowerShell script to verify Bug Bounty v4.0 HTML report and fixes
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host "🔍 Bug Bounty v4.0 Verification Script" -ForegroundColor Cyan  
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""

# Check if HTML report exists
$htmlReport = "bug_bounty_verification_report.html"
if (Test-Path $htmlReport) {
    Write-Host "✅ HTML Report found: $htmlReport" -ForegroundColor Green
    $reportSize = (Get-Item $htmlReport).Length
    Write-Host "📊 Report size: $([math]::Round($reportSize/1KB, 2)) KB" -ForegroundColor Blue
} else {
    Write-Host "❌ HTML Report not found!" -ForegroundColor Red
    exit 1
}

# Check Bug Bounty core file
$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"
if (Test-Path $bugBountyFile) {
    Write-Host "✅ BugBountyCore.js found" -ForegroundColor Green
    $coreSize = (Get-Item $bugBountyFile).Length
    Write-Host "📊 Core file size: $([math]::Round($coreSize/1MB, 2)) MB" -ForegroundColor Blue
} else {
    Write-Host "❌ BugBountyCore.js not found!" -ForegroundColor Red
    exit 1
}

Write-Host "`n🔍 Analyzing Bug Bounty Core File..." -ForegroundColor Yellow
Write-Host "=====================================" -ForegroundColor Yellow

# Read the core file content
$content = Get-Content $bugBountyFile -Raw

# Check for generic texts (should be 0)
Write-Host "`n📋 Checking for Generic Texts:" -ForegroundColor Magenta

$genericChecks = @{
    "payload_example" = 0
    "غير محدد" = 0  
    "لا توجد أدلة" = 0
    "تم تأكيد الثغرة" = 0
    "payload متخصص" = 0
    "معامل مكتشف أثناء الفحص" = 0
    "تم إرسال طلب مخصص" = 0
    "استجابة تؤكد وجود الثغرة" = 0
}

$totalGenericFound = 0
foreach ($text in $genericChecks.Keys) {
    $count = ([regex]::Matches($content, [regex]::Escape($text))).Count
    if ($count -eq $genericChecks[$text]) {
        Write-Host "✅ '$text': $count occurrences (Expected: $($genericChecks[$text]))" -ForegroundColor Green
    } else {
        Write-Host "❌ '$text': $count occurrences (Expected: $($genericChecks[$text]))" -ForegroundColor Red
        $totalGenericFound += $count
    }
}

# Check for new functions (should exist)
Write-Host "`n🔧 Checking for New Functions:" -ForegroundColor Magenta

$newFunctions = @(
    "generateRealPayload",
    "generateRealExploitationResult", 
    "generateRealEvidence",
    "generateRealResponse",
    "generateRealRequest",
    "generateRealParameter"
)

$functionsFound = 0
foreach ($func in $newFunctions) {
    if ($content -match "$func\s*\(") {
        Write-Host "✅ Function exists: $func" -ForegroundColor Green
        $functionsFound++
    } else {
        Write-Host "❌ Function missing: $func" -ForegroundColor Red
    }
}

# Check function usage
Write-Host "`n🎯 Checking Function Usage:" -ForegroundColor Magenta

$usageChecks = @{
    "this\.generateRealPayload\(" = "generateRealPayload usage"
    "this\.generateRealExploitationResult\(" = "generateRealExploitationResult usage"  
    "this\.generateRealEvidence\(" = "generateRealEvidence usage"
    "this\.generateRealResponse\(" = "generateRealResponse usage"
    "this\.generateRealRequest\(" = "generateRealRequest usage"
    "this\.generateRealParameter\(" = "generateRealParameter usage"
}

$totalUsages = 0
foreach ($pattern in $usageChecks.Keys) {
    $count = ([regex]::Matches($content, $pattern)).Count
    $description = $usageChecks[$pattern]
    if ($count -gt 0) {
        Write-Host "✅ $description: $count times" -ForegroundColor Green
        $totalUsages += $count
    } else {
        Write-Host "⚠️ $description: $count times" -ForegroundColor Yellow
    }
}

# Check for real content examples
Write-Host "`n📝 Checking for Real Content Examples:" -ForegroundColor Magenta

$realContentChecks = @{
    "تم استخراج.*سجل مستخدم" = "Real SQL exploitation results"
    "تم التقاط صور" = "Real evidence descriptions"
    "HTTP 200 OK.*تم إرجاع" = "Real server responses"
    "POST.*HTTP/1\.1.*تم إرسال" = "Real HTTP requests"
    "UNION SELECT.*users" = "Real SQL payloads"
    "script.*alert.*XSS" = "Real XSS payloads"
}

$realContentFound = 0
foreach ($pattern in $realContentChecks.Keys) {
    $description = $realContentChecks[$pattern]
    if ($content -match $pattern) {
        Write-Host "✅ Found: $description" -ForegroundColor Green
        $realContentFound++
    } else {
        Write-Host "❌ Missing: $description" -ForegroundColor Red
    }
}

# Calculate scores
Write-Host "`n📊 SCORING RESULTS:" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

$genericScore = if ($totalGenericFound -eq 0) { 30 } else { 0 }
$functionsScore = $functionsFound * 10
$usageScore = [math]::Min($totalUsages * 2, 20)
$contentScore = $realContentFound * 5

$totalScore = $genericScore + $functionsScore + $usageScore + $contentScore

Write-Host "🗑️ Generic texts removed: $genericScore/30 points" -ForegroundColor $(if($genericScore -eq 30) {"Green"} else {"Red"})
Write-Host "🔧 Functions added: $functionsScore/60 points ($functionsFound/6 functions)" -ForegroundColor $(if($functionsFound -eq 6) {"Green"} else {"Yellow"})
Write-Host "🎯 Function usage: $usageScore/20 points ($totalUsages usages)" -ForegroundColor $(if($usageScore -ge 15) {"Green"} else {"Yellow"})
Write-Host "📝 Real content: $contentScore/30 points ($realContentFound/6 examples)" -ForegroundColor $(if($realContentFound -ge 5) {"Green"} else {"Yellow"})

Write-Host "`n🏆 TOTAL SCORE: $totalScore/140" -ForegroundColor $(if($totalScore -ge 120) {"Green"} elseif($totalScore -ge 100) {"Yellow"} else {"Red"})

# Final assessment
Write-Host "`n🎯 FINAL ASSESSMENT:" -ForegroundColor Cyan
Write-Host "====================" -ForegroundColor Cyan

if ($totalScore -ge 120) {
    Write-Host "🎉 EXCELLENT! Bug Bounty v4.0 system successfully fixed!" -ForegroundColor Green
    Write-Host "✅ All generic texts removed" -ForegroundColor Green
    Write-Host "✅ All new functions implemented" -ForegroundColor Green  
    Write-Host "✅ Functions properly used throughout the code" -ForegroundColor Green
    Write-Host "✅ Real vulnerability-specific content generated" -ForegroundColor Green
    Write-Host "`n🚀 System is ready for production use!" -ForegroundColor Green
} elseif ($totalScore -ge 100) {
    Write-Host "👍 GOOD! Most fixes completed successfully" -ForegroundColor Yellow
    Write-Host "⚠️ Minor improvements may be needed" -ForegroundColor Yellow
} else {
    Write-Host "❌ NEEDS WORK! More fixes required" -ForegroundColor Red
    Write-Host "🔧 Please review and complete the remaining fixes" -ForegroundColor Red
}

# HTML Report verification
Write-Host "`n📄 HTML Report Verification:" -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan

$htmlContent = Get-Content $htmlReport -Raw
if ($htmlContent -match "تقرير التحقق من إصلاحات Bug Bounty v4.0") {
    Write-Host "✅ HTML report title correct" -ForegroundColor Green
} else {
    Write-Host "❌ HTML report title missing" -ForegroundColor Red
}

if ($htmlContent -match "98/100") {
    Write-Host "✅ HTML report shows correct score" -ForegroundColor Green
} else {
    Write-Host "⚠️ HTML report score may need updating" -ForegroundColor Yellow
}

if ($htmlContent -match "generateRealPayload") {
    Write-Host "✅ HTML report mentions new functions" -ForegroundColor Green
} else {
    Write-Host "❌ HTML report missing function details" -ForegroundColor Red
}

# Next steps
Write-Host "`n🚀 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan
Write-Host "1. Open HTML report: bug_bounty_verification_report.html" -ForegroundColor White
Write-Host "2. Start Bug Bounty server: localhost:3000" -ForegroundColor White  
Write-Host "3. Test real vulnerability scanning" -ForegroundColor White
Write-Host "4. Verify reports contain real content" -ForegroundColor White
Write-Host "5. Test screenshot functionality" -ForegroundColor White

Write-Host "`n✨ Verification completed successfully!" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Cyan
